@echo off
echo Installing Java 17 for GrocEase Backend...
echo ==========================================

REM Create Java directory
if not exist "C:\java" mkdir "C:\java"
cd "C:\java"

echo Downloading OpenJDK 17...
echo This may take a few minutes depending on your internet connection...

REM Download OpenJDK 17 (Eclipse Temurin)
powershell -Command "Invoke-WebRequest -Uri 'https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip' -OutFile 'openjdk17.zip'"

if not exist "openjdk17.zip" (
    echo ❌ Failed to download Java 17
    echo Trying alternative download...
    powershell -Command "Invoke-WebRequest -Uri 'https://download.oracle.com/java/17/archive/jdk-17.0.9_windows-x64_bin.zip' -OutFile 'openjdk17.zip'"
)

if not exist "openjdk17.zip" (
    echo ❌ Failed to download Java 17 from both sources
    echo Please download Java 17 manually from:
    echo https://adoptium.net/temurin/releases/?version=17
    pause
    exit /b 1
)

echo Extracting Java 17...
powershell -Command "Expand-Archive -Path 'openjdk17.zip' -DestinationPath '.'"

REM Find the extracted directory (it might have different names)
for /d %%d in (jdk-17*) do set JAVA17_DIR=%%d
for /d %%d in (OpenJDK17*) do set JAVA17_DIR=%%d

if "%JAVA17_DIR%"=="" (
    echo ❌ Could not find extracted Java 17 directory
    dir
    pause
    exit /b 1
)

echo Found Java 17 directory: %JAVA17_DIR%

REM Set up environment variables for this session
set JAVA_HOME=C:\java\%JAVA17_DIR%
set PATH=%JAVA_HOME%\bin;%PATH%

echo Testing Java 17 installation...
"%JAVA_HOME%\bin\java.exe" -version

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Java 17 installed successfully!
    echo.
    echo Java 17 is now available at: %JAVA_HOME%
    echo.
    echo To make this permanent, you can either:
    echo 1. Set JAVA_HOME environment variable to: C:\java\%JAVA17_DIR%
    echo 2. Or use the run-with-java17.bat script we'll create
    echo.
    echo Creating run-with-java17.bat script...
    
    REM Create a script to run the application with Java 17
    echo @echo off > ..\run-with-java17.bat
    echo echo Running GrocEase Backend with Java 17... >> ..\run-with-java17.bat
    echo echo ========================================== >> ..\run-with-java17.bat
    echo. >> ..\run-with-java17.bat
    echo REM Set Java 17 environment >> ..\run-with-java17.bat
    echo set JAVA_HOME=C:\java\%JAVA17_DIR% >> ..\run-with-java17.bat
    echo set PATH=%%JAVA_HOME%%\bin;%%PATH%% >> ..\run-with-java17.bat
    echo. >> ..\run-with-java17.bat
    echo echo Using Java version: >> ..\run-with-java17.bat
    echo java -version >> ..\run-with-java17.bat
    echo. >> ..\run-with-java17.bat
    echo echo Starting application... >> ..\run-with-java17.bat
    echo C:\maven\apache-maven-3.9.6\bin\mvn.cmd spring-boot:run >> ..\run-with-java17.bat
    echo. >> ..\run-with-java17.bat
    echo pause >> ..\run-with-java17.bat
    
    echo ✅ Created run-with-java17.bat script
    echo.
    echo You can now run the application with: .\run-with-java17.bat
    
) else (
    echo ❌ Java 17 installation failed
)

echo.
echo Cleaning up...
del openjdk17.zip

pause
