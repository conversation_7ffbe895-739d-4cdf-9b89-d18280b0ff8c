package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.product.CategoryDto;
import com.grocease.dto.product.ProductDto;
import com.grocease.service.ProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
@Slf4j
public class ProductController {

    private final ProductService productService;

    @GetMapping
    public ResponseEntity<PaginatedResponse<ProductDto>> getProducts(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {

        // Validate pagination parameters at controller level
        page = Math.max(0, page);
        limit = Math.max(1, Math.min(100, limit));

        log.info("Getting products - categoryId: {}, page: {}, limit: {}, search: {}",
                categoryId, page, limit, search);

        PaginatedResponse<ProductDto> response = productService.getProducts(
                categoryId, page, limit, search, sortBy, sortDir);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{productId}")
    public ResponseEntity<ApiResponse<ProductDto>> getProductById(@PathVariable Long productId) {
        log.info("Getting product by id: {}", productId);
        ProductDto product = productService.getProductById(productId);
        return ResponseEntity.ok(ApiResponse.success(product, "Product retrieved successfully"));
    }

    @GetMapping("/featured")
    public ResponseEntity<ApiResponse<List<ProductDto>>> getFeaturedProducts() {
        log.info("Getting featured products");
        List<ProductDto> products = productService.getFeaturedProducts();
        return ResponseEntity.ok(ApiResponse.success(products, "Featured products retrieved successfully"));
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<ProductDto>>> searchProducts(@RequestParam String query) {
        log.info("Searching products with query: {}", query);
        List<ProductDto> products = productService.searchProducts(query);
        return ResponseEntity.ok(ApiResponse.success(products, "Search results retrieved successfully"));
    }
}

@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
@Slf4j
class CategoryController {

    private final ProductService productService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<CategoryDto>>> getAllCategories() {
        log.info("Getting all categories");
        List<CategoryDto> categories = productService.getAllCategories();
        return ResponseEntity.ok(ApiResponse.success(categories, "Categories retrieved successfully"));
    }

    @GetMapping("/{categoryId}")
    public ResponseEntity<ApiResponse<CategoryDto>> getCategoryById(@PathVariable Long categoryId) {
        log.info("Getting category by id: {}", categoryId);
        CategoryDto category = productService.getCategoryById(categoryId);
        return ResponseEntity.ok(ApiResponse.success(category, "Category retrieved successfully"));
    }
}
